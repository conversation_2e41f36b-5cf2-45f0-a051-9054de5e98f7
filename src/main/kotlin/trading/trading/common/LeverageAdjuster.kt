package trading.trading.common

import io.micronaut.scheduling.annotation.Scheduled
import jakarta.inject.Singleton
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.slf4j.LoggerFactory
import trading.exchanges.ExchangeRouter
import trading.models.SymbolEx
import trading.storage.repos.SettingsRepository
import trading.storage.repos.StrategyRepository
import trading.telegram.TelegramSender
import trading.utils.LOW_PRIORITY_DISPATCHER
import java.util.concurrent.ConcurrentHashMap

@Singleton
class LeverageAdjuster(
    private val strategyRepository: StrategyRepository,
    private val exchangeRouter: ExchangeRouter,
    private val telegramSender: TelegramSender,
    private val settingsRepository: SettingsRepository
) {

    companion object {
        private val log = LoggerFactory.getLogger(LeverageAdjuster::class.java)
    }

    private val leveragesCache = ConcurrentHashMap<SymbolEx, Int>()

    @OptIn(DelicateCoroutinesApi::class)
    @Scheduled(initialDelay = "10s", fixedRate = "2m")
    fun adjustIfRequired() {
        GlobalScope.launch(LOW_PRIORITY_DISPATCHER) {
            strategyRepository.findAll().forEach {
                adjustIfRequired(it.legs.leg1.symbolEx, it.size)
                adjustIfRequired(it.legs.leg2.symbolEx, it.size)
            }
        }
    }

    /**
     * @param usdSize is USD size of a strategy
     */
    private suspend fun adjustIfRequired(symbolEx: SymbolEx, usdSize: Double) {
        val maxLeverage = settingsRepository.leverage

        // taking first leverage with contracts limit greater than required
        val riskLimits = kotlin.runCatching { exchangeRouter.getRiskLimits(symbolEx) }
            .getOrDefault(emptyList())
            .filter { it.leverage <= maxLeverage && it.usdSize > usdSize }
        val leverage = riskLimits.maxByOrNull { it.leverage }?.leverage ?: maxLeverage

        // not all exchanges can return current leverage, so we store it in cache
        val prevLeverage = kotlin.runCatching { exchangeRouter.getLeverage(symbolEx) }
            .getOrDefault(leveragesCache[symbolEx] ?: 0)

        if (prevLeverage != leverage) {
            kotlin.runCatching {
                exchangeRouter.setLeverage(symbolEx, leverage)
            }.onSuccess {
                leveragesCache[symbolEx] = leverage

                if (leverage < 5) {
                    log.warn("$symbolEx leverage is low: $leverage")
                }

                log.info("Leverage changed for $symbolEx from $prevLeverage to $leverage")
            }.onFailure {
                if (it !is NotImplementedError) {
                    log.warn("Failed to change leverage for $symbolEx from $prevLeverage to $leverage: ${it.message}. conf maxLeverage = $maxLeverage, usdSize = $usdSize, riskLimits = $riskLimits")
                }
            }
        }
    }
}
